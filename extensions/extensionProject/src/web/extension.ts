// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	console.log('Congratulations, your extension "extensionproject" is now active in the web extension host!');

	// 创建一个 TreeDataProvider 来提供视图内容
	const treeDataProvider = new MySearchTabProvider();

	// 注册 树视图
	const treeView = vscode.window.createTreeView('mySearchTabView', {
		treeDataProvider: treeDataProvider,
		showCollapseAll: false
	});

	// The command has been defined in the package.json file
	// Now provide the implementation of the command with registerCommand
	// The commandId parameter must match the command field in package.json
	const disposable = vscode.commands.registerCommand('extensionproject.helloWorld', () => {
		// The code you place here will be executed every time your command is executed

		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from extensionProject in a web extension host!');
	});

	// 注册命令：打开 WebView 面板
	const openNewTabCommand = vscode.commands.registerCommand('extensionproject.openNewTab', () => {
		MySearchWebviewPanel.createOrShow(context.extensionUri);
	});

	// 注册点击树项的命令
	const onDidSelectItem = vscode.commands.registerCommand('extensionproject.selectItem', (_item: MySearchTabItem) => {
		vscode.commands.executeCommand('extensionproject.openNewTab');
	});

	context.subscriptions.push(
		treeView,
		disposable,
		openNewTabCommand,
		onDidSelectItem
	);
}

class MySearchTabProvider implements vscode.TreeDataProvider<MySearchTabItem> {

	private _onDidChangeTreeData: vscode.EventEmitter<MySearchTabItem | undefined | null | void> = new vscode.EventEmitter<MySearchTabItem | undefined | null | void>();
	readonly onDidChangeTreeData: vscode.Event<MySearchTabItem | undefined | null | void> = this._onDidChangeTreeData.event;

	getTreeItem(element: MySearchTabItem): vscode.TreeItem {
		return element;
	}

	getChildren(element?: MySearchTabItem): Promise<MySearchTabItem[]> {
		if (!element) {
			// 根级别的项目
			return Promise.resolve([
				new MySearchTabItem('点击打开新标签页', vscode.TreeItemCollapsibleState.None, 'openTab')
			]);
		}
		return Promise.resolve([]);
	}
}

class MySearchTabItem extends vscode.TreeItem {

	constructor(
		label: string,
		collapsibleState: vscode.TreeItemCollapsibleState,
		public readonly itemType: string
	) {
		super(label, collapsibleState);

		this.tooltip = `${label}`;
		this.description = '';

		if (itemType === 'openTab') {
			this.command = {
				command: 'extensionproject.selectItem',
				title: 'Open New Tab',
				arguments: [this]
			};
			this.iconPath = new vscode.ThemeIcon('add');
		}
	}
}

/**
 * WebView 面板类，管理 WebView 的创建和生命周期
 */
class MySearchWebviewPanel {
	/**
	 * 跟踪当前活动的 WebView 面板
	 */
	public static currentPanel: MySearchWebviewPanel | undefined;

	public static readonly viewType = 'mySearchWebview';

	private readonly _panel: vscode.WebviewPanel;
	private readonly _extensionUri: vscode.Uri;
	private _disposables: vscode.Disposable[] = [];

	public static createOrShow(extensionUri: vscode.Uri) {
		const column = vscode.window.activeTextEditor
			? vscode.window.activeTextEditor.viewColumn
			: undefined;

		// 如果已经有一个面板，显示它
		if (MySearchWebviewPanel.currentPanel) {
			MySearchWebviewPanel.currentPanel._panel.reveal(column);
			return;
		}

		// 否则，创建一个新面板
		const panel = vscode.window.createWebviewPanel(
			MySearchWebviewPanel.viewType,
			'My DB WebView',
			column || vscode.ViewColumn.One,
			{
				// 启用 JavaScript
				enableScripts: true,
				// 限制 WebView 只能加载本地资源
				localResourceRoots: [vscode.Uri.joinPath(extensionUri, 'media')]
			}
		);

		MySearchWebviewPanel.currentPanel = new MySearchWebviewPanel(panel, extensionUri);
	}

	public static revive(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
		MySearchWebviewPanel.currentPanel = new MySearchWebviewPanel(panel, extensionUri);
	}

	private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
		this._panel = panel;
		this._extensionUri = extensionUri;

		// 设置初始 HTML 内容
		this._update();

		// 监听面板被关闭的事件
		this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

		// 处理来自 WebView 的消息
		this._panel.webview.onDidReceiveMessage(
			message => {
				switch (message.command) {
					case 'alert':
						vscode.window.showInformationMessage(message.text);
						return;
					case 'search':
						vscode.window.showInformationMessage(`搜索: ${message.query}`);
						return;
				}
			},
			null,
			this._disposables
		);
	}

	public dispose() {
		MySearchWebviewPanel.currentPanel = undefined;

		// 清理资源
		this._panel.dispose();

		while (this._disposables.length) {
			const x = this._disposables.pop();
			if (x) {
				x.dispose();
			}
		}
	}

	private _update() {
		const webview = this._panel.webview;
		this._panel.title = 'My DB WebView';
		this._panel.webview.html = this._getHtmlForWebview(webview);
	}

	private _getHtmlForWebview(webview: vscode.Webview) {
		return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My DB WebView</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .search-box {
            width: 100%;
            padding: 10px;
            margin: 20px 0;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-size: 14px;
        }
        .search-button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
        }
        .search-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            background-color: var(--vscode-panel-background);
        }
        .result-item {
            padding: 10px 0;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .result-title {
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
            margin-bottom: 5px;
        }
        .result-description {
            color: var(--vscode-descriptionForeground);
            font-size: 13px;
        }
        h1 {
            color: var(--vscode-titleBar-activeForeground);
            border-bottom: 2px solid var(--vscode-titleBar-border);
            padding-bottom: 10px;
        }
        .info-box {
            background-color: var(--vscode-textBlockQuote-background);
            border-left: 4px solid var(--vscode-textBlockQuote-border);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 My DB WebView</h1>

        <div class="info-box">
            <p><strong>欢迎使用 My DB WebView！</strong></p>
            <p>这是一个在 VS Code 中运行的 WebView 面板，你可以在这里进行搜索操作。</p>
        </div>

        <div>
            <input type="text" id="searchInput" class="search-box" placeholder="输入搜索关键词..." />
            <button class="search-button" onclick="performSearch()">🔍 搜索</button>
            <button class="search-button" onclick="clearResults()">🗑️ 清空</button>
            <button class="search-button" onclick="showAlert()">💡 测试消息</button>
        </div>

        <div id="results" class="results" style="display: none;">
            <h3>搜索结果</h3>
            <div id="resultsList"></div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function performSearch() {
            const query = document.getElementById('searchInput').value;
            if (!query.trim()) {
                showAlert('请输入搜索关键词！');
                return;
            }

            // 发送消息到扩展
            vscode.postMessage({
                command: 'search',
                query: query
            });

            // 显示模拟搜索结果
            showResults(query);
        }

        function showResults(query) {
            const resultsDiv = document.getElementById('results');
            const resultsList = document.getElementById('resultsList');

            // 模拟搜索结果
            const mockResults = [
                {
                    title: \`搜索结果 1 - \${query}\`,
                    description: \`这是关于 "\${query}" 的第一个搜索结果。包含相关信息和详细描述。\`
                },
                {
                    title: \`搜索结果 2 - \${query}\`,
                    description: \`这是关于 "\${query}" 的第二个搜索结果。提供更多相关内容。\`
                },
                {
                    title: \`搜索结果 3 - \${query}\`,
                    description: \`这是关于 "\${query}" 的第三个搜索结果。包含额外的参考信息。\`
                }
            ];

            resultsList.innerHTML = mockResults.map(result => \`
                <div class="result-item">
                    <div class="result-title">\${result.title}</div>
                    <div class="result-description">\${result.description}</div>
                </div>
            \`).join('');

            resultsDiv.style.display = 'block';
        }

        function clearResults() {
            document.getElementById('results').style.display = 'none';
            document.getElementById('searchInput').value = '';
        }

        function showAlert(message = '这是一个来自 WebView 的测试消息！') {
            vscode.postMessage({
                command: 'alert',
                text: message
            });
        }

        // 监听回车键
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('My DB WebView loaded successfully!');
        });
    </script>
</body>
		</html>`;
	}
}

// This method is called when your extension is deactivated
export function deactivate() { }
